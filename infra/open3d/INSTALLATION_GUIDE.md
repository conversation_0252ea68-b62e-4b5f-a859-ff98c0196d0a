Use the whl file called `open3d_cpu-0.19.0+083210b88-cp312-cp312-manylinux_2_39_x86_64.whl` on [Google Drive](https://drive.google.com/file/d/1dViJG4REqi_lcc2moZohNZ22jhhxZ7Di). Place it in the infra/open3d folder before doing `pipenv install` in the code directory.

If not, build from source:
1. Do https://www.open3d.org/docs/latest/compilation.html#ubuntu-22-04
2. Follow https://www.open3d.org/docs/latest/compilation.html#cloning-open3d
   - Make sure to run it in your pipenv (go to your project, do `pipenv shell` and go back to open3d directory).
   - If you run into trouble, use commit 1868f43 of Open3D. (See https://github.com/isl-org/Open3D/issues/7294)
   - Use sudo for `make install` and `make install-pip-package` if necessary.
