#!/usr/bin/env python3
"""
Script to build pyrealsense2 from source for Ubuntu 24.04
"""

import os
import subprocess
import sys
import shutil

def run_command(cmd, cwd=None):
    """Run a shell command and return success status"""
    print(f"Running: {cmd}")
    result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        return False
    print(f"Success: {result.stdout}")
    return True

def build_pyrealsense():
    """Build pyrealsense2 from source"""
    librealsense_dir = "/home/<USER>/realsense/librealsense"
    
    # Check if librealsense exists
    if not os.path.exists(librealsense_dir):
        print(f"Error: librealsense directory not found at {librealsense_dir}")
        return False
    
    # Create build directory for Python bindings
    python_build_dir = os.path.join(librealsense_dir, "build_python")
    os.makedirs(python_build_dir, exist_ok=True)
    
    # Get the current virtual environment's Python path
    python_path = sys.executable
    
    # Configure with CMake
    cmake_cmd = f"""cmake {librealsense_dir} \
        -DCMAKE_BUILD_TYPE=Release \
        -DBUILD_PYTHON_BINDINGS=true \
        -DPYTHON_EXECUTABLE={python_path} \
        -DPYTHON_INSTALL_DIR=$(python -c "import site; print(site.getsitepackages()[0])")"""
    
    if not run_command(cmake_cmd, cwd=python_build_dir):
        return False
    
    # Build the Python bindings
    if not run_command("make -j$(nproc) pyrealsense2", cwd=python_build_dir):
        return False
    
    # Install the Python bindings
    if not run_command("make install", cwd=python_build_dir):
        return False
    
    return True

if __name__ == "__main__":
    if build_pyrealsense():
        print("Successfully built pyrealsense2!")
    else:
        print("Failed to build pyrealsense2")
        sys.exit(1)
