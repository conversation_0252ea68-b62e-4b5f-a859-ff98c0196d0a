#!/usr/bin/env python3
"""
Basic RealSense pointcloud example
Captures depth and color frames and generates a pointcloud
"""

import pyrealsense2 as rs
import numpy as np
import cv2
import time

def main():
    # Configure depth and color streams
    pipeline = rs.pipeline()
    config = rs.config()
    
    # Get device product line for setting a supporting resolution
    pipeline_wrapper = rs.pipeline_wrapper(pipeline)
    pipeline_profile = config.resolve(pipeline_wrapper)
    device = pipeline_profile.get_device()
    device_product_line = str(device.get_info(rs.camera_info.product_line))
    
    found_rgb = False
    for s in device.sensors:
        if s.get_info(rs.camera_info.name) == 'RGB Camera':
            found_rgb = True
            break
    if not found_rgb:
        print("The demo requires Depth camera with Color sensor")
        exit(0)
    
    # Configure streams
    config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
    config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)
    
    # Start streaming
    pipeline.start(config)
    
    # Create a pointcloud object
    pc = rs.pointcloud()
    
    # We want the points object to be persistent so we can display the last cloud when a frame drops
    points = rs.points()
    
    try:
        print("Starting pointcloud capture...")
        print("Press 'q' to quit, 's' to save pointcloud")
        
        frame_count = 0
        while True:
            # Wait for a coherent pair of frames: depth and color
            frames = pipeline.wait_for_frames()
            depth_frame = frames.get_depth_frame()
            color_frame = frames.get_color_frame()
            
            if not depth_frame or not color_frame:
                continue
            
            # Convert images to numpy arrays
            depth_image = np.asanyarray(depth_frame.get_data())
            color_image = np.asanyarray(color_frame.get_data())
            
            # Apply colormap on depth image (image must be converted to 8-bit per pixel first)
            depth_colormap = cv2.applyColorMap(cv2.convertScaleAbs(depth_image, alpha=0.03), cv2.COLORMAP_JET)
            
            # Tell pointcloud object to map to this color frame
            pc.map_to(color_frame)
            
            # Generate the pointcloud and texture mappings
            points = pc.calculate(depth_frame)
            
            # Get vertices and texture coordinates
            vertices = np.asanyarray(points.get_vertices()).view(np.float32).reshape(-1, 3)
            tex_coords = np.asanyarray(points.get_texture_coordinates()).view(np.float32).reshape(-1, 2)
            
            # Filter out points that are too far (> 1 meter) or invalid
            valid_points = (vertices[:, 2] > 0) & (vertices[:, 2] < 1.0)
            vertices_filtered = vertices[valid_points]
            
            print(f"Frame {frame_count}: {len(vertices_filtered)} valid points (< 1m)")
            
            # Stack images horizontally for display
            images = np.hstack((color_image, depth_colormap))
            
            # Show images
            cv2.namedWindow('RealSense - Color & Depth', cv2.WINDOW_AUTOSIZE)
            cv2.imshow('RealSense - Color & Depth', images)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # Save pointcloud to PLY file
                filename = f"pointcloud_{frame_count:04d}.ply"
                print(f"Saving pointcloud to {filename}...")
                points.export_to_ply(filename, color_frame)
                print(f"Pointcloud saved!")
            
            frame_count += 1
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Stop streaming
        pipeline.stop()
        cv2.destroyAllWindows()
        print("Pointcloud capture stopped.")

if __name__ == "__main__":
    main()
