#!/usr/bin/env python3
"""
Advanced RealSense pointcloud example with temporal filtering
This example demonstrates depth post-processing filters including temporal filtering
"""

import pyrealsense2 as rs
import numpy as np
import cv2
import time
import argparse

def main():
    parser = argparse.ArgumentParser(description='Advanced RealSense pointcloud with temporal filtering')
    parser.add_argument('--enable-temporal', action='store_true', help='Enable temporal filter')
    parser.add_argument('--enable-spatial', action='store_true', help='Enable spatial filter')
    parser.add_argument('--enable-decimation', action='store_true', help='Enable decimation filter')
    parser.add_argument('--enable-hole-filling', action='store_true', help='Enable hole filling filter')
    parser.add_argument('--temporal-smooth-alpha', type=float, default=0.4, help='Temporal filter smooth alpha (0.0-1.0)')
    parser.add_argument('--temporal-smooth-delta', type=int, default=20, help='Temporal filter smooth delta')
    parser.add_argument('--temporal-holes-fill', type=int, default=1, help='Temporal filter holes fill mode')
    args = parser.parse_args()
    
    # Configure depth and color streams
    pipeline = rs.pipeline()
    config = rs.config()
    
    # Get device product line for setting a supporting resolution
    pipeline_wrapper = rs.pipeline_wrapper(pipeline)
    pipeline_profile = config.resolve(pipeline_wrapper)
    device = pipeline_profile.get_device()
    device_product_line = str(device.get_info(rs.camera_info.product_line))
    
    found_rgb = False
    for s in device.sensors:
        if s.get_info(rs.camera_info.name) == 'RGB Camera':
            found_rgb = True
            break
    if not found_rgb:
        print("The demo requires Depth camera with Color sensor")
        exit(0)
    
    # Configure streams
    config.enable_stream(rs.stream.depth, 640, 480, rs.format.z16, 30)
    config.enable_stream(rs.stream.color, 640, 480, rs.format.bgr8, 30)
    
    # Start streaming
    pipeline.start(config)
    
    # Create a pointcloud object
    pc = rs.pointcloud()
    
    # Create filters
    filters = []
    
    # Decimation filter - reduces the amount of data (optional)
    if args.enable_decimation:
        decimation = rs.decimation_filter()
        decimation.set_option(rs.option.filter_magnitude, 2)
        filters.append(("Decimation", decimation))
        print("✓ Decimation filter enabled")
    
    # Threshold filter - remove values outside recommended range
    threshold = rs.threshold_filter()
    threshold.set_option(rs.option.min_distance, 0.15)
    threshold.set_option(rs.option.max_distance, 4.0)
    filters.append(("Threshold", threshold))
    print("✓ Threshold filter enabled (0.15m - 4.0m)")
    
    # Spatial filter - edge-preserving spatial smoothing
    if args.enable_spatial:
        spatial = rs.spatial_filter()
        spatial.set_option(rs.option.filter_magnitude, 2)
        spatial.set_option(rs.option.filter_smooth_alpha, 0.5)
        spatial.set_option(rs.option.filter_smooth_delta, 20)
        spatial.set_option(rs.option.holes_fill, 0)
        filters.append(("Spatial", spatial))
        print("✓ Spatial filter enabled")
    
    # Temporal filter - reduces temporal noise
    if args.enable_temporal:
        temporal = rs.temporal_filter()
        temporal.set_option(rs.option.filter_smooth_alpha, args.temporal_smooth_alpha)
        temporal.set_option(rs.option.filter_smooth_delta, args.temporal_smooth_delta)
        temporal.set_option(rs.option.holes_fill, args.temporal_holes_fill)
        filters.append(("Temporal", temporal))
        print(f"✓ Temporal filter enabled (alpha={args.temporal_smooth_alpha}, delta={args.temporal_smooth_delta})")
    
    # Hole filling filter - fill holes in the depth map
    if args.enable_hole_filling:
        hole_filling = rs.hole_filling_filter()
        hole_filling.set_option(rs.option.holes_fill, 1)  # Fill from left
        filters.append(("Hole Filling", hole_filling))
        print("✓ Hole filling filter enabled")
    
    # Disparity transform for better temporal filtering
    depth_to_disparity = rs.disparity_transform(True)
    disparity_to_depth = rs.disparity_transform(False)
    
    # We want the points object to be persistent so we can display the last cloud when a frame drops
    points = rs.points()
    
    # Statistics tracking
    frame_count = 0
    start_time = time.time()
    processing_times = []
    
    try:
        print("\nStarting advanced pointcloud capture with filtering...")
        print("Controls:")
        print("  'q' - quit")
        print("  's' - save pointcloud to PLY file")
        print("  'r' - reset statistics")
        print("  'f' - toggle filter display")
        print("  SPACE - pause/resume")
        
        paused = False
        show_filter_info = True
        
        while True:
            if not paused:
                # Wait for a coherent pair of frames: depth and color
                frames = pipeline.wait_for_frames()
                depth_frame = frames.get_depth_frame()
                color_frame = frames.get_color_frame()
                
                if not depth_frame or not color_frame:
                    continue
                
                # Record processing start time
                process_start = time.time()
                
                # Apply filters to depth frame
                filtered_depth = depth_frame
                
                # Apply disparity transform before temporal filtering for better results
                if args.enable_temporal:
                    filtered_depth = depth_to_disparity.process(filtered_depth)
                
                # Apply all filters
                for filter_name, filter_obj in filters:
                    filtered_depth = filter_obj.process(filtered_depth)
                
                # Convert back to depth
                if args.enable_temporal:
                    filtered_depth = disparity_to_depth.process(filtered_depth)
                
                # Convert images to numpy arrays
                depth_image = np.asanyarray(filtered_depth.get_data())
                color_image = np.asanyarray(color_frame.get_data())
                
                # Apply colormap on depth image
                depth_colormap = cv2.applyColorMap(cv2.convertScaleAbs(depth_image, alpha=0.03), cv2.COLORMAP_JET)
                
                # Tell pointcloud object to map to this color frame
                pc.map_to(color_frame)
                
                # Generate the pointcloud and texture mappings
                points = pc.calculate(filtered_depth)
                
                # Get vertices and texture coordinates
                vertices = np.asanyarray(points.get_vertices()).view(np.float32).reshape(-1, 3)
                
                # Filter out points that are too far or invalid
                valid_points = (vertices[:, 2] > 0) & (vertices[:, 2] < 2.0)
                vertices_filtered = vertices[valid_points]
                
                # Calculate processing time
                process_time = (time.time() - process_start) * 1000  # Convert to milliseconds
                processing_times.append(process_time)
                
                # Keep only last 100 processing times for average
                if len(processing_times) > 100:
                    processing_times.pop(0)
                
                # Display statistics
                if show_filter_info:
                    fps = frame_count / (time.time() - start_time) if frame_count > 0 else 0
                    avg_process_time = np.mean(processing_times)
                    
                    info_text = [
                        f"Frame: {frame_count}",
                        f"FPS: {fps:.1f}",
                        f"Points: {len(vertices_filtered)}",
                        f"Process: {avg_process_time:.1f}ms",
                        f"Filters: {len(filters)}"
                    ]
                    
                    # Add filter info to display
                    y_offset = 30
                    for i, text in enumerate(info_text):
                        cv2.putText(color_image, text, (10, y_offset + i*25), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                    
                    # Add active filter names
                    filter_names = [name for name, _ in filters]
                    if filter_names:
                        filter_text = "Active: " + ", ".join(filter_names)
                        cv2.putText(color_image, filter_text, (10, y_offset + len(info_text)*25), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
                
                # Stack images horizontally for display
                images = np.hstack((color_image, depth_colormap))
                
                frame_count += 1
            
            # Show images
            cv2.namedWindow('RealSense - Advanced Filtering', cv2.WINDOW_AUTOSIZE)
            cv2.imshow('RealSense - Advanced Filtering', images)
            
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # Save pointcloud to PLY file
                timestamp = int(time.time())
                filename = f"filtered_pointcloud_{timestamp}.ply"
                print(f"Saving pointcloud to {filename}...")
                points.export_to_ply(filename, color_frame)
                print(f"Pointcloud saved with {len(vertices_filtered)} points!")
            elif key == ord('r'):
                # Reset statistics
                frame_count = 0
                start_time = time.time()
                processing_times = []
                print("Statistics reset!")
            elif key == ord('f'):
                # Toggle filter info display
                show_filter_info = not show_filter_info
                print(f"Filter info display: {'ON' if show_filter_info else 'OFF'}")
            elif key == ord(' '):
                # Toggle pause
                paused = not paused
                print(f"{'Paused' if paused else 'Resumed'}")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Stop streaming
        pipeline.stop()
        cv2.destroyAllWindows()
        
        # Print final statistics
        if frame_count > 0:
            total_time = time.time() - start_time
            avg_fps = frame_count / total_time
            avg_process_time = np.mean(processing_times) if processing_times else 0
            
            print("\n" + "="*50)
            print("FINAL STATISTICS")
            print("="*50)
            print(f"Total frames processed: {frame_count}")
            print(f"Total time: {total_time:.2f}s")
            print(f"Average FPS: {avg_fps:.2f}")
            print(f"Average processing time: {avg_process_time:.2f}ms")
            print(f"Active filters: {len(filters)}")
            for name, _ in filters:
                print(f"  - {name}")
        
        print("Advanced pointcloud capture stopped.")

if __name__ == "__main__":
    main()
