#!/usr/bin/env python3
"""
Test script to verify pyrealsense2 installation
"""

try:
    import pyrealsense2 as rs
    print("✓ pyrealsense2 imported successfully!")
    
    # Check version
    print(f"RealSense library version: {rs.__version__}")
    
    # Try to create a context
    ctx = rs.context()
    print("✓ Context created successfully!")
    
    # Check for connected devices
    devices = ctx.query_devices()
    print(f"Number of connected devices: {len(devices)}")
    
    if len(devices) > 0:
        for i, device in enumerate(devices):
            print(f"Device {i}: {device.get_info(rs.camera_info.name)}")
    else:
        print("No RealSense devices detected. Connect a device to test streaming.")
        
except ImportError as e:
    print(f"✗ Error importing pyrealsense2: {e}")
except Exception as e:
    print(f"✗ Error: {e}")
