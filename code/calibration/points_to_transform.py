import numpy as np
from numpy.typing import NDArray

from pose import <PERSON><PERSON>


def get_pose_from_three_points(
    origin: Pose | NDArray[np.floating],
    point_on_x_axis: Pose | NDArray[np.floating],
    point_on_xy_plane: Pose | NDArray[np.floating],
) -> Pose:
    """
    Gets the transformation matrix from the origin and two points on the x and y axes.

    Args:
        origin: The origin of the coordinate system.
        point_on_x_axis: Point on the x axis with x>0. Defines the x axis.
        point_on_xy_plane: Point with z=0, y>0, x!=0. Defines the y axis, which will be orthogonalized to the x direction.

    Returns:
        The transformation matrix as a Pose.

    Raises:
        ValueError: If input vectors are zero, parallel, or would result in invalid transformation.
    """
    if isinstance(origin, Pose):
        origin = origin.t
    if isinstance(point_on_x_axis, Pose):
        point_on_x_axis = point_on_x_axis.t
    if isinstance(point_on_xy_plane, Pose):
        point_on_xy_plane = point_on_xy_plane.t

    # Get direction vectors
    x_direction = point_on_x_axis - origin
    y_direction = point_on_xy_plane - origin

    # Check for zero vectors
    x_norm = np.linalg.norm(x_direction)
    y_norm = np.linalg.norm(y_direction)

    if x_norm == 0.0:
        raise ValueError("x_direction cannot be a zero vector")
    if y_norm == 0.0:
        raise ValueError("y_direction cannot be a zero vector")

    # Normalize x_direction first
    x_direction = x_direction / x_norm

    # Orthogonalize y_direction to x_direction using Gram-Schmidt
    y_direction = y_direction - np.dot(y_direction, x_direction) * x_direction

    # Check if y_direction became zero after orthogonalization (parallel vectors)
    y_norm_after_ortho = np.linalg.norm(y_direction)
    if y_norm_after_ortho == 0.0:
        raise ValueError("x_direction and y_direction cannot be parallel")

    # Normalize the orthogonalized y_direction
    y_direction = y_direction / y_norm_after_ortho

    # Compute z_direction as cross product
    z_direction = np.cross(x_direction, y_direction)

    # Build the transformation matrix
    transform = np.eye(4)
    transform[:3, 0] = x_direction
    transform[:3, 1] = y_direction
    transform[:3, 2] = z_direction
    transform[:3, 3] = origin
    return Pose.from_SE3(transform)
