from functools import cache

import numpy as np
import open3d as o3d
import tomlkit

from box_top import BoxTop, ConveyorBox
from calibration.points_to_transform import get_pose_from_three_points
from pose import Pose
from robot.constrain_angle import constrain_angle


def read_as_array(config_element) -> np.ndarray:
    return np.array(config_element.unwrap())  # type: ignore


@cache
def get_zone_pose(zone) -> Pose:
    with open("config/conveyor.toml", "r") as f:
        config = tomlkit.load(f)
    origin = read_as_array(config["robot_poses"]["zone"][f"{zone}"]["ds"])[:3] * 0.001  # type: ignore
    if zone == 0:
        x_point = read_as_array(config["robot_poses"]["zone"][f"{zone + 1}"]["ds"])[:3] * 0.001  # type: ignore
    else:
        x_point = read_as_array(config["robot_poses"]["zone"][f"{zone - 1}"]["ds"])[:3] * 0.001  # type: ignore
    y_point = read_as_array(config["robot_poses"]["zone"][f"{zone}"]["os"])[:3] * 0.001  # type: ignore
    return get_pose_from_three_points(origin, x_point, y_point)


@cache
def get_zone_pose_rotation(zone) -> np.ndarray:
    with open("config/conveyor.toml", "r") as f:
        config = tomlkit.load(f)
    return read_as_array(config["robot_poses"]["zone"][f"{zone}"]["ds"])[3:6]  # type: ignore


@cache
def get_conveyor_width() -> float:
    with open("config/conveyor.toml", "r") as f:
        config = tomlkit.load(f)
    return config["calibration"]["roi"].unwrap()[1]  # type: ignore


@cache
def get_gripper_size() -> np.ndarray:
    with open("config/conveyor.toml", "r") as f:
        config = tomlkit.load(f)
    return read_as_array(config["corrections"]["gripper_size"]) * 0.001  # type: ignore


@cache
def get_offset_zone_space() -> np.ndarray:
    with open("config/conveyor.toml", "r") as f:
        config = tomlkit.load(f)
    return read_as_array(config["corrections"]["offset_zone_space"])  # type: ignore


def get_box_in_zone_pick_pose(zone: int, box: ConveyorBox) -> Pose:
    # the box is touching the sensor = touching the plane x=0
    obb_top = o3d.geometry.OrientedBoundingBox(
        center=[0, 0, 0],
        extent=box.top.extent,
        R=o3d.geometry.get_rotation_matrix_from_zxy(box.top.rotation),
    )
    aabb_top = obb_top.get_axis_aligned_bounding_box()
    x_position = -aabb_top.min_bound[0]

    # y is in other direction across the conveyor
    y_position = get_conveyor_width() - box.y_position

    z_position = box.top.z_position

    position_in_zone = np.array([x_position, y_position, z_position, 1.0])

    gripper_size = get_gripper_size()
    position_in_zone[0] -= gripper_size[0] / 2
    position_in_zone[1] -= gripper_size[1] / 2

    offset_zone_space = get_offset_zone_space()
    position_in_zone[0] += offset_zone_space[0]  # TODO do cleaner with numpy
    position_in_zone[1] += offset_zone_space[1]
    position_in_zone[2] += offset_zone_space[2]

    zone_transform = get_zone_pose(zone)
    position = zone_transform @ position_in_zone

    rotation = get_zone_pose_rotation(zone)
    # rotation[2] += box.top.rotation[0]
    # rotation[2] = constrain_angle(rotation[2])

    return Pose(
        x=position[0] * 1000,
        y=position[1] * 1000,
        z=position[2] * 1000,
        rx=rotation[0],
        ry=rotation[1],
        rz=rotation[2],
    )


if __name__ == "__main__":
    box = ConveyorBox(
        y_position=np.float64(0.3654072880744934),
        top=BoxTop(
            center=np.array([0.61087638, 0.36540729, 0.22356918]),
            z_position=0.22565340539020928,
            extent=np.array([0.3198784, 0.30068812, 0.02632343]),
            rotation=np.array([90.61325428, 0.50586676, 178.72995058]),
            volume=0.0025318828166273327,
        ),
        extent=np.array([0.3198784, 0.30068812, 0.24989261]),
        volume=0.0025318828166273327,
    )
    print(get_box_in_zone_pick_pose(3, box))
