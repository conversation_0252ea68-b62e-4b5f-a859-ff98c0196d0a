"""Unit tests for visualizer classes."""

import pytest
from unittest.mock import Mock, patch
import open3d as o3d
from box_top.visualizer import NoOpVisualizer, Open3DVisualizer


class TestNoOpVisualizer:
    """Test cases for NoOpVisualizer class."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test fixtures."""
        self.visualizer = NoOpVisualizer()

    def test_noop_visualizer_creation(self):
        """Test NoOpVisualizer creation."""
        assert isinstance(self.visualizer, NoOpVisualizer)

    def test_noop_update(self):
        """Test NoOpVisualizer update always returns True."""
        # Create mock geometries
        pcd = o3d.geometry.PointCloud()
        obb = o3d.geometry.OrientedBoundingBox()
        outlier_cloud = o3d.geometry.PointCloud()

        result = self.visualizer.update(pcd, obb, outlier_cloud)
        assert result

    def test_noop_should_stop(self):
        """Test NoOpVisualizer should_stop always returns False."""
        result = self.visualizer.should_stop()
        assert not result

    def test_noop_context_manager(self):
        """Test NoOpVisualizer as context manager."""
        with self.visualizer:
            assert True  # Should work without issues


class TestOpen3DVisualizer:
    """Test cases for Open3DVisualizer class."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test fixtures."""
        self.visualizer = Open3DVisualizer()

    def test_open3d_visualizer_creation(self):
        """Test Open3DVisualizer creation."""
        assert isinstance(self.visualizer, Open3DVisualizer)
        assert not self.visualizer._initialized
        assert not self.visualizer._should_stop
        assert self.visualizer._visualizer is None

    def test_initial_state(self):
        """Test initial state of Open3DVisualizer."""
        assert not self.visualizer._initialized
        assert not self.visualizer._should_stop
        assert self.visualizer._pcd_geometry is None
        assert self.visualizer._obb_geometry is None
        assert self.visualizer._outlier_geometry is None

    @patch("open3d.visualization.VisualizerWithKeyCallback")
    def test_initialize_success(self, mock_visualizer_class):
        """Test successful initialization."""
        # Mock the Open3D visualizer
        mock_visualizer = Mock()
        mock_visualizer_class.return_value = mock_visualizer
        mock_visualizer.create_window.return_value = True
        mock_visualizer.register_key_callback.return_value = True
        mock_visualizer.get_render_option.return_value = Mock()

        result = self.visualizer._initialize()

        assert result
        assert self.visualizer._initialized
        mock_visualizer.create_window.assert_called_once()
        mock_visualizer.register_key_callback.assert_called_once()

    @patch("open3d.visualization.VisualizerWithKeyCallback")
    def test_initialize_failure(self, mock_visualizer_class):
        """Test initialization failure."""
        # Mock the Open3D visualizer to raise an exception
        mock_visualizer_class.side_effect = Exception("Failed to create visualizer")

        result = self.visualizer._initialize()

        assert not result
        assert not self.visualizer._initialized

    def test_initialize_already_initialized(self):
        """Test initialize when already initialized."""
        # Manually set as initialized
        self.visualizer._initialized = True

        result = self.visualizer._initialize()

        assert result

    def test_should_stop_initial(self):
        """Test should_stop returns False initially."""
        result = self.visualizer.should_stop()
        assert not result

    def test_escape_key_callback(self):
        """Test escape key callback sets should_stop to True."""
        # Call the escape key callback
        result = self.visualizer._escape_key_callback(None)

        assert self.visualizer._should_stop
        assert not result  # Should return False to indicate key was handled

    def test_cleanup_not_initialized(self):
        """Test cleanup when not initialized."""
        # Should not raise any exceptions
        self.visualizer._cleanup()
        assert not self.visualizer._initialized

    @patch("open3d.visualization.VisualizerWithKeyCallback")
    def test_cleanup_initialized(self, mock_visualizer_class):
        """Test cleanup when initialized."""
        # Set up mock visualizer
        mock_visualizer = Mock()
        self.visualizer._visualizer = mock_visualizer
        self.visualizer._initialized = True

        self.visualizer._cleanup()

        mock_visualizer.destroy_window.assert_called_once()
        assert self.visualizer._visualizer is None
        assert not self.visualizer._initialized

    @patch("open3d.visualization.VisualizerWithKeyCallback")
    def test_cleanup_with_exception(self, mock_visualizer_class):
        """Test cleanup handles exceptions gracefully."""
        # Set up mock visualizer that raises exception on destroy
        mock_visualizer = Mock()
        mock_visualizer.destroy_window.side_effect = Exception("Cleanup failed")
        self.visualizer._visualizer = mock_visualizer
        self.visualizer._initialized = True

        # Should not raise exception
        self.visualizer._cleanup()

        # Should still clean up state
        assert self.visualizer._visualizer is None
        assert not self.visualizer._initialized

    def test_update_not_initialized(self):
        """Test update when not initialized."""
        pcd = o3d.geometry.PointCloud()
        obb = o3d.geometry.OrientedBoundingBox()
        outlier_cloud = o3d.geometry.PointCloud()

        result = self.visualizer.update(pcd, obb, outlier_cloud)

        assert not result

    def test_update_should_stop(self):
        """Test update when should_stop is True."""
        self.visualizer._initialized = True
        self.visualizer._visualizer = Mock()
        self.visualizer._should_stop = True

        pcd = o3d.geometry.PointCloud()
        obb = o3d.geometry.OrientedBoundingBox()
        outlier_cloud = o3d.geometry.PointCloud()

        result = self.visualizer.update(pcd, obb, outlier_cloud)

        assert not result

    def test_context_manager(self):
        """Test Open3DVisualizer as context manager."""
        pcd = o3d.geometry.PointCloud()
        obb = o3d.geometry.OrientedBoundingBox()
        outlier_cloud = o3d.geometry.PointCloud()

        with self.visualizer:
            assert self.visualizer._initialized
            assert self.visualizer._visualizer is not None
            assert not self.visualizer._should_stop
            assert not self.visualizer.should_stop()
            assert self.visualizer.update(pcd, obb, outlier_cloud)
        assert not self.visualizer._initialized
        assert self.visualizer._visualizer is None

    def test_update_twice(self):
        """Test Open3DVisualizer as context manager."""
        pcd = o3d.geometry.PointCloud()
        obb = o3d.geometry.OrientedBoundingBox()
        outlier_cloud = o3d.geometry.PointCloud()

        with self.visualizer:
            assert self.visualizer.update(pcd, obb, outlier_cloud)
            assert self.visualizer.update(pcd, obb, outlier_cloud)

    @patch("open3d.visualization.VisualizerWithKeyCallback")
    def test_context_manager_success(self, mock_visualizer_class):
        """Test Open3DVisualizer as context manager with successful initialization."""
        # Mock successful initialization
        mock_visualizer = Mock()
        mock_visualizer_class.return_value = mock_visualizer
        mock_visualizer.create_window.return_value = True
        mock_visualizer.register_key_callback.return_value = True
        mock_visualizer.get_render_option.return_value = Mock()

        with self.visualizer:
            assert self.visualizer._initialized

        # Should have called cleanup
        mock_visualizer.destroy_window.assert_called_once()

    @patch("open3d.visualization.VisualizerWithKeyCallback")
    def test_context_manager_initialization_failure(self, mock_visualizer_class):
        """Test Open3DVisualizer context manager with initialization failure."""
        # Mock failed initialization
        mock_visualizer_class.side_effect = Exception("Failed to create visualizer")

        with pytest.raises(RuntimeError):
            with self.visualizer:
                ...
