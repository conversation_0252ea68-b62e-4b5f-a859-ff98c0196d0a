"""Unit tests for BoxTop class."""

import pytest
import numpy as np
from box_top import BoxTop


class TestBoxTop:
    """Test cases for BoxTop class."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test fixtures."""
        self.center = np.array([0.1, 0.2, 0.8])
        self.z_position = 0.75
        self.extent = np.array([0.3, 0.2, 0.1])
        self.rotation = np.array([45.0, 10.0, -5.0])
        self.volume = 0.006

        self.box_top = BoxTop(
            center=self.center,
            z_position=self.z_position,
            extent=self.extent,
            rotation=self.rotation,
            volume=self.volume,
        )

    def test_box_top_creation(self):
        """Test that BoxTop can be created with valid parameters."""
        assert isinstance(self.box_top, BoxTop)
        np.testing.assert_array_equal(self.box_top.center, self.center)
        assert self.box_top.z_position == self.z_position
        np.testing.assert_array_equal(self.box_top.extent, self.extent)
        np.testing.assert_array_equal(self.box_top.rotation, self.rotation)
        assert self.box_top.volume == self.volume

    def test_box_top_center_access(self):
        """Test accessing center coordinates."""
        assert self.box_top.center[0] == 0.1  # x
        assert self.box_top.center[1] == 0.2  # y
        assert self.box_top.center[2] == 0.8  # z

    def test_box_top_extent_access(self):
        """Test accessing extent dimensions."""
        assert self.box_top.extent[0] == 0.3  # width
        assert self.box_top.extent[1] == 0.2  # height
        assert self.box_top.extent[2] == 0.1  # depth

    def test_box_top_rotation_access(self):
        """Test accessing rotation angles."""
        assert self.box_top.rotation[0] == 45.0  # z rotation
        assert self.box_top.rotation[1] == 10.0  # x rotation
        assert self.box_top.rotation[2] == -5.0  # y rotation

    def test_box_top_fields_comparison(self):
        """Test BoxTop fields can be compared individually."""
        other_box_top = BoxTop(
            center=self.center.copy(),
            z_position=self.z_position,
            extent=self.extent.copy(),
            rotation=self.rotation.copy(),
            volume=self.volume,
        )
        # Compare individual fields since dataclass with numpy arrays doesn't support direct equality
        np.testing.assert_array_equal(self.box_top.center, other_box_top.center)
        assert self.box_top.z_position == other_box_top.z_position
        np.testing.assert_array_equal(self.box_top.extent, other_box_top.extent)
        np.testing.assert_array_equal(self.box_top.rotation, other_box_top.rotation)
        assert self.box_top.volume == other_box_top.volume

    def test_box_top_string_representation(self):
        """Test BoxTop string representation."""
        str_repr = str(self.box_top)
        assert "BoxTop" in str_repr
        assert "center" in str_repr
        assert "z_position" in str_repr
        assert "extent" in str_repr
        assert "rotation" in str_repr
        assert "volume" in str_repr

    def test_box_top_with_negative_coordinates(self):
        """Test BoxTop with negative coordinates."""
        negative_center = np.array([-0.1, -0.2, 0.5])
        box_top = BoxTop(
            center=negative_center,
            z_position=0.4,
            extent=np.array([0.1, 0.1, 0.1]),
            rotation=np.array([0.0, 0.0, 0.0]),
            volume=0.001,
        )
        np.testing.assert_array_equal(box_top.center, negative_center)
        assert box_top.center[0] == -0.1
        assert box_top.center[1] == -0.2

    def test_box_top_with_zero_volume(self):
        """Test BoxTop with zero volume (edge case)."""
        box_top = BoxTop(
            center=np.array([0.0, 0.0, 0.0]),
            z_position=0.0,
            extent=np.array([0.0, 0.0, 0.0]),
            rotation=np.array([0.0, 0.0, 0.0]),
            volume=0.0,
        )
        assert box_top.volume == 0.0
        np.testing.assert_array_equal(box_top.extent, np.array([0.0, 0.0, 0.0]))
