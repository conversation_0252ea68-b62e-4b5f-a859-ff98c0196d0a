from functools import partial

import tomlkit


def _constrain_angle(min: float, max: float, angle: float) -> float:
    """Constrains an angle to a min and max value, wrapping around if necessary at 180°."""
    while angle < min:
        angle += 180
    while angle > max:
        angle -= 180
    return angle


with open("config/robot.toml", "r") as f:
    config = tomlkit.load(f)
min_z_angle: float = config["opcua"]["min_z_angle"].unwrap()  # type: ignore
max_z_angle: float = config["opcua"]["max_z_angle"].unwrap()  # type: ignore

assert min_z_angle < max_z_angle
assert max_z_angle - min_z_angle > 180
constrain_angle = partial(_constrain_angle, min_z_angle, max_z_angle)
