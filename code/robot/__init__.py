from dataclasses import dataclass


@dataclass
class Pose:
    """Pose in mm and degrees."""

    x: float
    y: float
    z: float
    rx: float
    ry: float
    rz: float

    def __add__(self, other: "Pose") -> "Pose":
        return Pose(
            self.x + other.x,
            self.y + other.y,
            self.z + other.z,
            self.rx + other.rx,
            self.ry + other.ry,
            self.rz + other.rz,
        )
