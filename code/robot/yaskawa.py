import asyncio
from contextlib import asynccontextmanager
from enum import Enum
from typing import TypedDict

import tomlkit

from comm_opcua import OPCUAClient, OPCUANode
from robot import Po<PERSON>

from loguru import logger


class Program(Enum):
    IDLE = 0
    PICK_PLACE = 1


class Nodes(TypedDict):
    """TypedDict for the nodes. Make sure the name matches that on server side."""

    program_id: OPCUANode[int]
    pre_pick_speed: OPCUANode[int]
    pre_pick_pose_x: OPCUANode[int]
    pre_pick_pose_y: OPCUANode[int]
    pre_pick_pose_z: OPCUANode[int]
    pre_pick_pose_rx: OPCUANode[int]
    pre_pick_pose_ry: OPCUANode[int]
    pre_pick_pose_rz: OPCUANode[int]
    pick_speed: OPCUANode[int]
    pick_pose_x: OPCUANode[int]
    pick_pose_y: OPCUANode[int]
    pick_pose_z: OPCUANode[int]
    pick_pose_rx: OPCUANode[int]
    pick_pose_ry: OPCUANode[int]
    pick_pose_rz: OPCUANode[int]
    fly_by_speed: OPCUANode[int]
    fly_by_pose_x: OPCUANode[int]
    fly_by_pose_y: OPCUANode[int]
    fly_by_pose_z: OPCUANode[int]
    fly_by_pose_rx: OPCUANode[int]
    fly_by_pose_ry: OPCUANode[int]
    fly_by_pose_rz: OPCUANode[int]
    pre_place_speed: OPCUANode[int]
    pre_place_pose_x: OPCUANode[int]
    pre_place_pose_y: OPCUANode[int]
    pre_place_pose_z: OPCUANode[int]
    pre_place_pose_rx: OPCUANode[int]
    pre_place_pose_ry: OPCUANode[int]
    pre_place_pose_rz: OPCUANode[int]
    place_speed: OPCUANode[int]
    place_pose_x: OPCUANode[int]
    place_pose_y: OPCUANode[int]
    place_pose_z: OPCUANode[int]
    place_pose_rx: OPCUANode[int]
    place_pose_ry: OPCUANode[int]
    place_pose_rz: OPCUANode[int]
    robot_conveyor_lock: OPCUANode[bool]
    robot_lost_cargo: OPCUANode[bool]
    robot_unreachable: OPCUANode[bool]
    robot_no_pick: OPCUANode[bool]


class YaskawaRobot:
    def __init__(self, client: OPCUAClient):
        self.client = client
        self.nodes: Nodes

    async def pick_place(self, pick: Pose, fly_by: Pose, place: Pose, speed: float):
        await self.client.loudly_wait_for_connection()

        logger.debug("Waiting for robot to be ready...")
        await self._wait_idle()

        logger.debug("Setting poses")
        await self._set_pre_pick(pick + Pose(0, 0, 100, 0, 0, 0), speed)
        await self._set_pick(pick, speed)
        await self._set_fly_by(fly_by, speed)
        await self._set_pre_place(place + Pose(0, 0, 100, 0, 0, 0), speed)
        await self._set_place(place, speed)

        logger.debug("Starting pick and place")
        await self._start_program(Program.PICK_PLACE)

        logger.debug("Waiting for robot to finish...")
        await self._wait_idle()
        logger.debug("Pick and place done!")

    @asynccontextmanager
    async def connect(self):
        try:
            await self.client.connect()

            # Build the nodes dictionary by calling client.get_node for each node
            self.nodes: Nodes = {}  # type: ignore
            for node_name, node_type in Nodes.__annotations__.items():
                self.nodes[node_name] = await self.client.get_node(node_name, node_type)

            yield self.client
        except Exception as e:  # noqa: E722
            logger.warning("Closing OPC-UA connection on error")
            logger.exception(e)
            await self.client._client.disconnect()

    async def _set_pre_pick(self, pose: Pose, speed: float):
        await self.nodes["pre_pick_speed"].set(int(speed * 10000))  # m/s → 0.1mm/s
        await self.nodes["pre_pick_pose_x"].set(int(pose.x * 1000))  # mm → μm
        await self.nodes["pre_pick_pose_y"].set(int(pose.y * 1000))
        await self.nodes["pre_pick_pose_z"].set(int(pose.z * 1000))
        await self.nodes["pre_pick_pose_rx"].set(int(pose.rx * 10000))  # deg → 0.0001deg
        await self.nodes["pre_pick_pose_ry"].set(int(pose.ry * 10000))
        await self.nodes["pre_pick_pose_rz"].set(int(pose.rz * 10000))

    async def _set_pick(self, pose: Pose, speed: float):
        await self.nodes["pick_speed"].set(int(speed * 10000))  # m/s → 0.1mm/s
        await self.nodes["pick_pose_x"].set(int(pose.x * 1000))  # mm → μm
        await self.nodes["pick_pose_y"].set(int(pose.y * 1000))
        await self.nodes["pick_pose_z"].set(int(pose.z * 1000))
        await self.nodes["pick_pose_rx"].set(int(pose.rx * 10000))  # deg → 0.0001deg
        await self.nodes["pick_pose_ry"].set(int(pose.ry * 10000))
        await self.nodes["pick_pose_rz"].set(int(pose.rz * 10000))

    async def _set_fly_by(self, pose: Pose, speed: float):
        await self.nodes["fly_by_speed"].set(int(speed * 10000))  # m/s → 0.1mm/s
        await self.nodes["fly_by_pose_x"].set(int(pose.x * 1000))  # mm → μm
        await self.nodes["fly_by_pose_y"].set(int(pose.y * 1000))
        await self.nodes["fly_by_pose_z"].set(int(pose.z * 1000))
        await self.nodes["fly_by_pose_rx"].set(int(pose.rx * 10000))  # deg → 0.0001deg
        await self.nodes["fly_by_pose_ry"].set(int(pose.ry * 10000))
        await self.nodes["fly_by_pose_rz"].set(int(pose.rz * 10000))

    async def _set_pre_place(self, pose: Pose, speed: float):
        await self.nodes["pre_place_speed"].set(int(speed * 10000))  # m/s → 0.1mm/s
        await self.nodes["pre_place_pose_x"].set(int(pose.x * 1000))  # mm → μm
        await self.nodes["pre_place_pose_y"].set(int(pose.y * 1000))
        await self.nodes["pre_place_pose_z"].set(int(pose.z * 1000))
        await self.nodes["pre_place_pose_rx"].set(int(pose.rx * 10000))  # deg → 0.0001deg
        await self.nodes["pre_place_pose_ry"].set(int(pose.ry * 10000))
        await self.nodes["pre_place_pose_rz"].set(int(pose.rz * 10000))

    async def _set_place(self, pose: Pose, speed: float):
        await self.nodes["place_speed"].set(int(speed * 10000))  # m/s → 0.1mm/s
        await self.nodes["place_pose_x"].set(int(pose.x * 1000))  # mm → μm
        await self.nodes["place_pose_y"].set(int(pose.y * 1000))
        await self.nodes["place_pose_z"].set(int(pose.z * 1000))
        await self.nodes["place_pose_rx"].set(int(pose.rx * 10000))  # deg → 0.0001deg
        await self.nodes["place_pose_ry"].set(int(pose.ry * 10000))
        await self.nodes["place_pose_rz"].set(int(pose.rz * 10000))

    async def _wait_idle(self):
        await self.nodes["program_id"].wait_for_value(Program.IDLE.value)

    async def _start_program(self, program: Program):
        await self.nodes["program_id"].set(program.value)


def build_production():
    with open("config/robot.toml", "r") as f:
        config = tomlkit.load(f)
    return YaskawaRobot(
        OPCUAClient(
            f"opc.tcp://{config['opcua']['ip']}:{config['opcua']['port']}/",  # pyright: ignore[reportIndexIssue]
            config["opcua"]["namespace"],  # pyright: ignore[reportArgumentType,reportIndexIssue]
        )
    )


async def main():
    robot = build_production()
    async with robot.connect():
        await robot.pick_place(
            pick=Pose(100, -1200, 330, -180, 0, 25),
            fly_by=Pose(800, -900, 650, -180, 0, 70),
            place=Pose(1760, 320, -458, -180, 0, 115),
            speed=0.1,  # 10 cm/s
        )


if __name__ == "__main__":
    asyncio.run(main())
