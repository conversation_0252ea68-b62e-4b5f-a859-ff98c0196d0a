import asyncio
import random

from loguru import logger

import conveyor.zoned_conveyor as zoned_conveyor
import robot.yaskawa as yaskawa

ACTIVE_ZONES = [2, 3]  # Temporarily for testing: only use last 2 zones


async def build_production():
    conveyor = zoned_conveyor.build_production()
    robot = yaskawa.build_production()
    return conveyor, robot


async def main():
    conveyor, robot = await build_production()
    async with conveyor.connect(), robot.connect():
        filled_zones = await conveyor.get_filled_zones()
        filled_zones = [zone for zone in filled_zones if zone in ACTIVE_ZONES]
        if not filled_zones:
            logger.warning("No filled zones")
            return
        random_zone = random.choice(filled_zones)
        logger.debug(f"Picking from zone {random_zone}")


if __name__ == "__main__":
    asyncio.run(main(), debug=True)
