# Wiring Diagram for Conveyor Control System

## Arduino Pin Connections

### Digital Inputs (Sensors)
```
Pin 2  ← Part 1 Sensor (with pullup resistor)
Pin 3  ← Part 2 Sensor (with pullup resistor)
Pin 4  ← Part 3 Sensor (with pullup resistor)
Pin 5  ← Part 4 Sensor (with pullup resistor)
```

### Digital Outputs (Motors)
```
Pin 6  → Part 1 Motor Driver
Pin 7  → Part 2 Motor Driver
Pin 8  → Part 3 Motor Driver
Pin 9  → Part 4 Motor Driver
```

### Digital Outputs (Status LEDs)
```
Pin 10 → Part 1 LED (+ 220Ω resistor)
Pin 11 → Part 2 LED (+ 220Ω resistor)
Pin 12 → Part 3 LED (+ 220Ω resistor)
Pin 13 → Part 4 LED (+ 220Ω resistor)
```

### Optional (Emergency Stop)
```
Pin A0 ← Emergency Stop Button (with pullup resistor)
```

## Sensor Wiring
Each proximity sensor should be wired as follows:
```
Sensor VCC → Arduino 5V
Sensor GND → Arduino GND
Sensor OUT → Arduino Digital Pin (2-5)
```

## Motor Driver Wiring
For each motor driver (e.g., L298N, relay module):
```
Driver VCC → External Power Supply (12V/24V)
Driver GND → Common Ground (Arduino GND + Power Supply GND)
Driver IN  → Arduino Digital Pin (6-9)
Driver OUT → Motor Terminals
```

## LED Wiring
For each status LED:
```
Arduino Pin → 220Ω Resistor → LED Anode
LED Cathode → Arduino GND
```

## Power Supply Considerations
- Arduino: 5V via USB or external adapter
- Motors: Separate power supply (12V or 24V depending on motors)
- Sensors: Usually 5V or 12V (check sensor specifications)
- Common ground connection between all components

## Safety Notes
- Always use appropriate fuses for motor circuits
- Ensure proper grounding of all components
- Use optoisolators for high-voltage motor drivers
- Install physical emergency stop switches
- Test all connections before powering on

## Troubleshooting Tips
1. **Sensors not detecting**: Check wiring and power supply
2. **Motors not running**: Verify motor driver connections and power
3. **False triggers**: Adjust sensor positioning or debounce settings
4. **System freezing**: Check for loose connections or power issues
