from dataclasses import dataclass

@dataclass
class Box:
    width: int | float
    depth: int | float
    height: int | float

    x = 0
    y = 0
    z = 0

    roll = 0
    pitch = 0
    yaw = 0
    
    isPacked = False

    def __post_init__(self):
        self.dim = (self.width, self.depth, self.height)
        self.position = (self.x,self.y,self.z)

    def place(self, x, y, z):
        self.x = x
        self.y = y
        self.z = z
        self.position = (self.x,self.y,self.z)
