import cv2
import os
import re


def create_video_from_images():
    # Path to the folder containing your images
    image_folder = 'images'  # Set this to your directory with the images
    video_name = 'output_video.mp4'  # Output video file name

    # Function to extract numeric part from the filename for correct sorting
    def extract_number(filename):
        match = re.search(r'(\d+)', filename)  # Extracts digits from the filename
        return int(match.group(1)) if match else -1

    # Get list of image files with correct sorting
    images = sorted([img for img in os.listdir(image_folder) if img.endswith(".png")], key=extract_number)

    # Read the first image to get width and height
    first_image_path = os.path.join(image_folder, images[0])
    frame = cv2.imread(first_image_path)
    height, width, layers = frame.shape

    # Define the video codec and create VideoWriter object
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Codec for .mp4 files
    video = cv2.VideoWriter(video_name, fourcc, 4, (width, height))  # 4 FPS

    # Loop through all images and write them to the video
    for image in images:
        img_path = os.path.join(image_folder, image)
        frame = cv2.imread(img_path)
        video.write(frame)

    # Hold the last frame for 1 second (30 frames)
    for _ in range(10):
        video.write(frame)

    # Release the video writer
    video.release()

    print(f"Video saved as {video_name}")
