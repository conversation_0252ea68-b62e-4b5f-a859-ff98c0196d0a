import asyncio
from contextlib import AsyncExitStack, asynccontextmanager
from pprint import pprint

import tomlkit

from conveyor import Zone
from conveyor.conveylinx import ConveyLinx, ConveyorStopStatus, ModuleStatus, ZoneStatus
from loguru import logger


class ZonedConveyor:
    """
    Class to manage a conveyor with multiple zones where every controller controls two zones.
    List of controllers should be ordered from upstream to downstream.
    """

    def __init__(self, controllers: list[ConveyLinx]):
        self.controllers = controllers
        self.num_zones = len(controllers) * 2
        self.polling_interval = 0.1

    async def get_zone_status(self) -> list[ZoneStatus]:
        return [
            await self.controllers[i // 2].get_local_status(Zone.UPSTREAM if i % 2 == 0 else Zone.DOWNSTREAM)
            for i in range(self.num_zones)
        ]

    async def get_module_status(self) -> list[ModuleStatus]:
        return [await controller.get_module_status() for controller in self.controllers]

    async def jog_forward(self, zone: int = None, enable=True) -> None:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for controller in self.controllers:
                await controller.jog_forward(Zone.UPSTREAM, enable)
                await controller.jog_forward(Zone.DOWNSTREAM, enable)
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            await self.controllers[controller_index].jog_forward(controller_zone, enable)

    async def jog_reverse(self, zone: int = None, enable=True) -> None:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for controller in self.controllers:
                await controller.jog_reverse(Zone.UPSTREAM, enable)
                await controller.jog_reverse(Zone.DOWNSTREAM, enable)
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            await self.controllers[controller_index].jog_reverse(controller_zone, enable)

    async def set_accumulation(self, zone: int = None, enable=True) -> None:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for controller in self.controllers:
                await controller.set_accumulation(Zone.UPSTREAM, enable)
                await controller.set_accumulation(Zone.DOWNSTREAM, enable)
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            await self.controllers[controller_index].set_accumulation(controller_zone, enable)

    async def set_maintenance_mode(self, zone: int = None, enable=True) -> None:
        """
        Set maintenance mode. This will stop the conveyor and disable all motion in this zone only.
        Notice that this happens in a "dirty" way: the controller might see the effects as a jam
        when it would have wanted to discharge from this zone to downstream.

        You can use this for blocking upstream if you're only clearing this zone but no downstream zones.
        """
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for controller in self.controllers:
                await controller.set_maintenance_mode(Zone.UPSTREAM, enable)
                await controller.set_maintenance_mode(Zone.DOWNSTREAM, enable)
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            await self.controllers[controller_index].set_maintenance_mode(controller_zone, enable)

    async def release_and_accumulate_downstream_discharge(self) -> bool:
        """
        Release and accumulate the downstream discharge zone.
        Returns True if successful, False if no box to discharge.
        """
        zone_status = await self.controllers[-1].get_local_status(Zone.DOWNSTREAM)
        if zone_status != ZoneStatus.BLOCKED_STOPPED:
            logger.warning("Discharging non existing box!")
            return False
        await self.controllers[-1].release_and_accumulate(Zone.DOWNSTREAM)
        return True

    async def is_upstream_induct_free(self) -> bool:
        return await self._is_status(0, [ZoneStatus.CLEAR_STOPPED, ZoneStatus.BLOCKED_RUNNING])

    async def wait_upstream_induct_free(self) -> None:
        while not await self.is_upstream_induct_free():
            await asyncio.sleep(self.polling_interval)

    async def upstream_induct(self, enable, tracking_word_1: int = 0, tracking_word_2: int = 0) -> None:
        await self.controllers[0].upstream_induct(enable, tracking_word_1, tracking_word_2)

    async def get_tracking(self) -> list[tuple[int, int]]:
        tracking = []
        for controller in self.controllers:
            tracking.append(await controller.get_local_tracking(Zone.UPSTREAM))
            tracking.append(await controller.get_local_tracking(Zone.DOWNSTREAM))
        return tracking

    async def _is_status(self, zone: int | None, status: ZoneStatus | list[ZoneStatus]) -> bool:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for zone_num in range(self.num_zones):
                if not await self._is_status(zone_num, status):
                    return False
            return True
        else:
            controller_index = zone // 2
            controller_zone = Zone.UPSTREAM if zone % 2 == 0 else Zone.DOWNSTREAM
            current_status = await self.controllers[controller_index].get_local_status(controller_zone)
            if isinstance(status, list):
                return current_status in status
            return current_status == status

    async def is_stopped(self, zone: int = None) -> bool:
        return await self._is_status(zone, [ZoneStatus.CLEAR_STOPPED, ZoneStatus.BLOCKED_STOPPED, ZoneStatus.BUSY])

    async def wait_stopped(self, zone: int = None) -> None:
        while not await self.is_stopped(zone):
            await asyncio.sleep(self.polling_interval)

    async def is_blocked_stopped(self, zone: int = None) -> bool:
        return await self._is_status(zone, ZoneStatus.BLOCKED_STOPPED)

    async def is_all_downstream_blocked_stopped(self, zone_from: int) -> bool:
        for zone in range(zone_from, self.num_zones):
            if not await self.is_blocked_stopped(zone):
                return False
        return True

    async def wait_all_downstream_blocked_stopped(self, zone_from: int) -> None:
        while not await self.is_all_downstream_blocked_stopped(zone_from):
            await asyncio.sleep(self.polling_interval)

    async def set_conveystop(self, zone: int = None, enable: bool = True) -> None:
        """
        Set convey stop. This will stop the conveyor and disable all motion for the whole module.
        This happens in a cleaner way than maintenance mode: the controller will not get confused
        by its own actions. The downside is that the granularity is on the module level instead of
        the zone level (compared with using maintenance mode).
        """
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            for controller in self.controllers:
                await controller.set_conveystop(enable)
        else:
            controller_index = zone // 2
            await self.controllers[controller_index].set_conveystop(enable)

    async def get_convey_stop_status(self, zone: int = None) -> list[ConveyorStopStatus]:
        assert zone is None or 0 <= zone < self.num_zones
        if zone is None:
            return [await controller.get_convey_stop_status() for controller in self.controllers]
        else:
            controller_index = zone // 2
            return [await self.controllers[controller_index].get_convey_stop_status()]

    async def get_filled_zones(self) -> list[int]:
        return [i for i, zone in enumerate(await self.get_zone_status()) if zone == ZoneStatus.BLOCKED_STOPPED]

    @asynccontextmanager
    async def wait_box_lock_zone(self, zone: int):
        # NOTE: Using conveystop now, in some situations it might be less time efficient,
        # but it will also not break the ZPA.
        await self.wait_all_downstream_blocked_stopped(zone)
        # await self.set_maintenance_mode(zone, True)
        await self.set_conveystop(zone, True)
        yield
        # await self.set_maintenance_mode(zone, False)
        await self.set_conveystop(zone, False)

    @asynccontextmanager
    async def connect(self):
        async with AsyncExitStack() as stack:
            for controller in self.controllers:
                await stack.enter_async_context(controller.connect())
            yield self


def build_production():
    with open("config/conveyor.toml", "r") as f:
        config = tomlkit.load(f)

    return ZonedConveyor([ConveyLinx(ip_address) for ip_address in config["ip_addresses"].unwrap()])


async def main():
    conveyor = build_production()
    async with conveyor.connect() as conveyor:
        # logger.debug("Connected to conveyor")
        # pprint(await conveyor.get_module_status())
        # pprint(await conveyor.get_tracking())
        # await conveyor.release_and_accumulate_downstream_discharge()
        # await conveyor.upstream_induct(True, 3, 6)
        # # # # pprint(await conveyor.get_tracking())
        # # # # await asyncio.sleep(10)
        # # # await conveyor.upstream_induct(False)
        # while not await conveyor.is_stopped():
        #     pprint(await conveyor.get_tracking())
        #     await asyncio.sleep(0.4)
        # pprint(await conveyor.get_tracking())
        # async with conveyor.wait_box_lock_zone(2):
        #     pprint(await conveyor.get_convey_stop_status())
        #     await asyncio.sleep(20)
        # pprint([i["stop_active_due_to_stop_command"] for i in await conveyor.get_convey_stop_status()])
        # await conveyor.set_conveystop(None, False)
        # pprint([i["stop_active_due_to_stop_command"] for i in await conveyor.get_convey_stop_status()])
        await conveyor.set_maintenance_mode(None, False)


if __name__ == "__main__":
    asyncio.run(main())
