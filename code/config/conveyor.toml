# ===================== #
# General configuration #
# ===================== #

ip_addresses = [
  "*************",
  "*************",
] # In order: upstream → downstream

# ================================= #
# Robot - conveyor calibration part #
# ================================= #

[robot_poses]
# zone.0 = most upstream zone
# ds = drive side, left when looking from upstream to downstream
# os = opperator side, right when looking from upstream to downstream
# [ x,  y,  z,  rx,  ry,  rz]
# [mm, mm, mm, deg, deg, deg]
zone.0.ds = []
zone.0.os = []
zone.1.ds = [-921.246, -1091.567, 107.368, 179.2034, 0.4525, 30.1337]
zone.1.os = []
zone.2.ds = [-222.401, -1089.527, 110.509, 178.8677, 0.4009, 30.1126]
zone.2.os = [-215.944, -1772.805, 123.001, 178.6849, 0.1990, 30.0562]
zone.3.ds = [478.218, -1084.740, 117.397, 179.0494, 0.1845, 30.1325]
zone.3.os = [480.826, -1769.690, 130.791, 178.6156, 0.1390, 30.1319]

[corrections]
gripper_size = [0.299, 0.129]
# Offsets will be added to the final pick pose
offset_zone_space = [-0.008, 0.0, 0.0] # [m, m, m] (aruco edge)
offset_base = [0.0, 0.0, 0.0]

# ============================================================ #
# Camera - conveyor calibration part (to be filled in by user) #
# ============================================================ #

[boxes]
min_box_dimension = 0.09 # 9 cm
max_box_height = 0.45    # 45 cm

[height]
deadzone_above_conveyor = 0.09 # 9 cm
marker_thickness = 0.002       # 2 mm

[markers]
upstream_marker_id = 7
width_marker_id = 3
downstream_marker_id = 6

# =============================================================================================== #
# Values below are calculated automatically by running calibration/camera_conveyor_calibration.py #
# =============================================================================================== #

[calibration]
pose = [
  [
    0.007790199683758997,
    0.9997737729273175,
    0.019791810315474674,
    0.5549465001972841,
  ],
  [
    0.9999518973980606,
    -0.007906491866293523,
    0.005804332553096291,
    0.5324998530836109,
  ],
  [
    0.005959503243212453,
    0.019745641368281844,
    -0.9997872743579254,
    1.3325682332643747,
  ],
  [
    0.0,
    0.0,
    0.0,
    1.0,
  ],
]
roi = [1.1977780222463654, 0.8028812473368472]
